import { mutation, MutationCtx, QueryCtx, query } from "../../../../convex/_generated/server";
import { InvitationsGetRequestDTO, InvitationsGetRequestDTOObjectValidator, InvitationsGetResponseDTO, InvitationsGetResponseDTOValidator } from "../../../../types/dtos";
import { Roles } from "../../../../types/enums";
import { withLoggedIn } from "../../../../wrappers/auth/withLoggedIn";

// inspect (fetch) an invitation to recover important info to be displayed in the accept UI
export const inspect = query({
    args: InvitationsGetRequestDTOObjectValidator,
    returns: InvitationsGetResponseDTOValidator,
    handler: withLoggedIn(async (ctx: QueryCtx, user, args: InvitationsGetRequestDTO): Promise<InvitationsGetResponseDTO> => {
        const invitation = await ctx.db.get<"invitations">(args.id);
        if (invitation?.email !== user.email) {
            
        }
        if (!invitation) {
            throw new Error("Invitation not found");
        }
        return invitation;
    })

})

