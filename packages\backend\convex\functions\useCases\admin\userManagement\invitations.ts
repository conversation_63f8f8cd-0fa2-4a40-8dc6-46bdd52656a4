import { mutation, MutationCtx, QueryCtx, query } from "../../../../../convex/_generated/server";
import { withRole } from "../../../../../wrappers/auth/withRole";
import {
    InvitationsListRequestDTOObjectValidator,
    InvitationsListResponseDTOValidator,
    InvitationsListRequestDTO,
    InvitationsListResponseDTO,
    InvitationsGetRequestDTOObjectValidator,
    InvitationsGetResponseDTOValidator,
    InvitationsGetRequestDTO,
    InvitationsGetResponseDTO,
    InvitationsInviteRequestDTOObjectValidator,
    InvitationsInviteRequestDTO,
    InvitationsInviteResponseDTOValidator,
    InvitationsInviteResponseDTO,
    InvitationsBulkInviteRequestDTOObjectValidator,
    InvitationsBulkInviteResponseDTOValidator,
    InvitationsBulkInviteRequestDTO,
    InvitationsBulkInviteResponseDTO,
    InvitationsCancelRequestDTOObjectValidator,
    InvitationsCancelResponseDTOValidator,
    InvitationsCancelRequestDTO,
    InvitationsCancelResponseDTO,
    InvitationsBulkCancelRequestDTOObjectValidator,
    InvitationsBulkCancelResponseDTOValidator,
    InvitationsBulkCancelRequestDTO,
    InvitationsBulkCancelResponseDTO,
    InvitationsResendRequestDTOObjectValidator,
    InvitationsResendResponseDTOValidator,
    InvitationsResendRequestDTO,
    InvitationsResendResponseDTO,
    InvitationsBulkResendRequestDTOObjectValidator,
    InvitationsBulkResendResponseDTOValidator,
    InvitationsBulkResendRequestDTO,
    InvitationsBulkResendResponseDTO
} from "../../../../../types/dtos";
import { Roles } from "../../../../../types/enums";
import { Doc } from "../../../../_generated/dataModel";
import { getCurrentUser } from "../../../../../helpers/auth/getCurrentUser";

// List all invitations
export const list = query({
    args: InvitationsListRequestDTOObjectValidator,
    returns: InvitationsListResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: QueryCtx, args: InvitationsListRequestDTO): Promise<InvitationsListResponseDTO> => {
        // Use search index if search is provided, which also supports role filtering
        let response: Doc<"invitations">[] = [];
        if (args.search) {
            const invitations = await ctx.db
                .query("invitations")
                .withSearchIndex("search_email", q => {
                    let query = q.search("email", args.search);
                    // Apply role filter if roles are specified
                    if (args.role && args.role.length > 0) {
                        query = query.eq("roleSuggested", args.role);
                    }
                    return query;
                })
                .take(args.limit + (args.offset || 0));

            response = args.offset ? invitations.slice(args.offset) : invitations;
        } else if (args.role && args.role.length > 0) {
            // Use regular index for role filtering when no search is provided
            const invitations = await ctx.db
                .query("invitations")
                .withIndex("by_roles", q =>
                    q.eq("roleSuggested", args.role)
                )
                .order("desc")
                .take(args.limit + (args.offset || 0));

            response = args.offset ? invitations.slice(args.offset) : invitations;
        } else {
            // No filters - use full table scan
            const invitations = await ctx.db
                .query("invitations")
                .order("desc")
                .take(args.limit + (args.offset || 0));

            response = args.offset ? invitations.slice(args.offset) : invitations;
        }

        // TODO: use ShardedCounter instead of this extra expensive query
        const total = (await ctx.db.query("invitations").collect()).length;

        return {
            data: response,
            total,
        };
    }),
})

// Get a specific invitation by id
export const get = query({
    args: InvitationsGetRequestDTOObjectValidator,
    returns: InvitationsGetResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: QueryCtx, args: InvitationsGetRequestDTO): Promise<InvitationsGetResponseDTO> => {
        const invitation = await ctx.db.get<"invitations">(args.id);
        if (!invitation) {
            throw new Error("Invitation not found");
        }
        return invitation;
    })
})

// Invite a user
export const invite = mutation({
    args: InvitationsInviteRequestDTOObjectValidator,
    returns: InvitationsInviteResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsInviteRequestDTO): Promise<InvitationsInviteResponseDTO> => {
        const currentUser = await getCurrentUser(ctx);
        if (!currentUser) {
            throw new Error("User not found");
        }
        const invitation = await ctx.db.insert("invitations", {
            email: args.email,
            roleSuggested: [args.role],
            token: crypto.randomUUID().toString(),
            invitedBy: currentUser._id,
            accepted: false,
            createdAt: new Date().toISOString(),
        });
        // send invitation email to the user
        
        return {
            success: true,
            message: "success",
        };
    })
})

// // Bulk invite users
// export const bulkInvite = mutation({
//     args: InvitationsBulkInviteRequestDTOObjectValidator,
//     returns: InvitationsBulkInviteResponseDTOValidator,
//     handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsBulkInviteRequestDTO): Promise<InvitationsBulkInviteResponseDTO> => {
//     })
// })

// // Cancel an invitation
// export const cancel = mutation({
//     args: InvitationsCancelRequestDTOObjectValidator,
//     returns: InvitationsCancelResponseDTOValidator,
//     handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsCancelRequestDTO): Promise<InvitationsCancelResponseDTO> => {
//     })
// })

// // Bulk cancel invitations
// export const bulkCancel = mutation({
//     args: InvitationsBulkCancelRequestDTOObjectValidator,
//     returns: InvitationsBulkCancelResponseDTOValidator,
//     handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsBulkCancelRequestDTO): Promise<InvitationsBulkCancelResponseDTO> => {
//     })
// })

// // Resend the invitation email
// export const resend = mutation({
//     args: InvitationsResendRequestDTOObjectValidator,
//     returns: InvitationsResendResponseDTOValidator,
//     handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsResendRequestDTO): Promise<InvitationsResendResponseDTO> => {
//     })
// })

// // Bulk resend invitations
// export const bulkResend = mutation({
//     args: InvitationsBulkResendRequestDTOObjectValidator,
//     returns: InvitationsBulkResendResponseDTOValidator,
//     handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsBulkResendRequestDTO): Promise<InvitationsBulkResendResponseDTO> => {
//     })
// })
