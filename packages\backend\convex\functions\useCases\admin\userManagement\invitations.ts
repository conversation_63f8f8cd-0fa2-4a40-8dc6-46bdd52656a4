import { mutation, MutationCtx, QueryCtx, query } from "../../../../../convex/_generated/server";
import { withRole } from "../../../../../wrappers/auth/withRole";
import {
    InvitationsListRequestDTOObjectValidator,
    InvitationsListResponseDTOValidator,
    InvitationsListRequestDTO,
    InvitationsListResponseDTO,
    InvitationsGetRequestDTOObjectValidator,
    InvitationsGetResponseDTOValidator,
    InvitationsGetRequestDTO,
    InvitationsGetResponseDTO,
    InvitationsInviteRequestDTOObjectValidator,
    InvitationsInviteRequestDTO,
    InvitationsInviteResponseDTOValidator,
    InvitationsInviteResponseDTO,
    InvitationsBulkInviteRequestDTOObjectValidator,
    InvitationsBulkInviteResponseDTOValidator,
    InvitationsBulkInviteRequestDTO,
    InvitationsBulkInviteResponseDTO,
    InvitationsCancelRequestDTOObjectValidator,
    InvitationsCancelResponseDTOValidator,
    InvitationsCancelRequestDTO,
    InvitationsCancelResponseDTO,
    InvitationsBulkCancelRequestDTOObjectValidator,
    InvitationsBulkCancelResponseDTOValidator,
    InvitationsBulkCancelRequestDTO,
    InvitationsBulkCancelResponseDTO,
    InvitationsResendRequestDTOObjectValidator,
    InvitationsResendResponseDTOValidator,
    InvitationsResendRequestDTO,
    InvitationsResendResponseDTO,
    InvitationsBulkResendRequestDTOObjectValidator,
    InvitationsBulkResendResponseDTOValidator,
    InvitationsBulkResendRequestDTO,
    InvitationsBulkResendResponseDTO
} from "../../../../../types/dtos";
import { Roles } from "../../../../../types/enums";
import { Doc } from "../../../../_generated/dataModel";
import { getCurrentUser } from "../../../../../helpers/auth/getCurrentUser";
import { internal } from "../../../../_generated/api";

// List all invitations
export const list = query({
    args: InvitationsListRequestDTOObjectValidator,
    returns: InvitationsListResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: QueryCtx, args: InvitationsListRequestDTO): Promise<InvitationsListResponseDTO> => {
        // Use search index if search is provided, which also supports role filtering
        let response: Doc<"invitations">[] = [];
        if (args.search) {
            const invitations = await ctx.db
                .query("invitations")
                .withSearchIndex("search_email", q => {
                    let query = q.search("email", args.search);
                    // Apply role filter if roles are specified
                    if (args.role && args.role.length > 0) {
                        query = query.eq("roleSuggested", args.role);
                    }
                    return query;
                })
                .take(args.limit + (args.offset || 0));

            response = args.offset ? invitations.slice(args.offset) : invitations;
        } else if (args.role && args.role.length > 0) {
            // Use regular index for role filtering when no search is provided
            const invitations = await ctx.db
                .query("invitations")
                .withIndex("by_roles", q =>
                    q.eq("roleSuggested", args.role)
                )
                .order("desc")
                .take(args.limit + (args.offset || 0));

            response = args.offset ? invitations.slice(args.offset) : invitations;
        } else {
            // No filters - use full table scan
            const invitations = await ctx.db
                .query("invitations")
                .order("desc")
                .take(args.limit + (args.offset || 0));

            response = args.offset ? invitations.slice(args.offset) : invitations;
        }

        // TODO: use ShardedCounter instead of this extra expensive query
        const total = (await ctx.db.query("invitations").collect()).length;

        return {
            data: response,
            total,
        };
    }),
})

// Get a specific invitation by id
export const get = query({
    args: InvitationsGetRequestDTOObjectValidator,
    returns: InvitationsGetResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: QueryCtx, args: InvitationsGetRequestDTO): Promise<InvitationsGetResponseDTO> => {
        const invitation = await ctx.db.get<"invitations">(args.id);
        if (!invitation) {
            throw new Error("Invitation not found");
        }
        return invitation;
    })
})

// Invite a user
export const invite = mutation({
    args: InvitationsInviteRequestDTOObjectValidator,
    returns: InvitationsInviteResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsInviteRequestDTO): Promise<InvitationsInviteResponseDTO> => {
        const currentUser = await getCurrentUser(ctx);
        if (!currentUser) {
            return {
                success: false,
                message: "user.not_found",
            };
        }

        // check if a user with this email already exists
        const existingUser = await ctx.db.query("users").withIndex("email", q => q.eq("email", args.email)).unique();
        if (existingUser) {
            return {
                success: false,
                message: "invitation.user_already_exists",
            };
        }

        // Generate a unique token for the invitation
        const token = crypto.randomUUID().toString();

        // Create the invitation record
        const invitation = await ctx.db.insert("invitations", {
            email: args.email,
            roleSuggested: [args.role],
            token,
            invitedBy: currentUser._id,
            accepted: false,
            createdAt: new Date().toISOString(),
        });

        // Schedule the email to be sent asynchronously
        await ctx.scheduler.runAfter(0, internal.functions.sendInvitationEmail.sendInvitationEmail, {
            email: args.email,
            token,
            inviterName: currentUser.name || currentUser.email,
            role: args.role,
        });

        return {
            success: true,
            message: "invitation.invite_success",
        };
    })
})

// Bulk invite users
export const bulkInvite = mutation({
    args: InvitationsBulkInviteRequestDTOObjectValidator,
    returns: InvitationsBulkInviteResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsBulkInviteRequestDTO): Promise<InvitationsBulkInviteResponseDTO> => {
        const currentUser = await getCurrentUser(ctx);
        if (!currentUser) {
            return {
                success: false,
                message: "user.not_found",
            };
        }

        let invitedUsersCount = 0;
        let existingUsersCount = 0;


        for (const email of args.emails) {
            // check if a user with this email already exists
            const existingUser = await ctx.db.query("users").withIndex("email", q => q.eq("email", email)).unique();
            if (existingUser) {
                existingUsersCount++;
                continue;
            }

            // Generate a unique token for the invitation

            const token = crypto.randomUUID().toString();

            // Create the invitation record
            const invitation = await ctx.db.insert("invitations", {
                email,
                roleSuggested: [args.role],
                token,
                invitedBy: currentUser._id,
                accepted: false,
                createdAt: new Date().toISOString(),
            });

            // Schedule the email to be sent asynchronously
            await ctx.scheduler.runAfter(0, internal.functions.sendInvitationEmail.sendInvitationEmail, {
                email,
                token,
                inviterName: currentUser.name || currentUser.email,
                role: args.role,
            });
            invitedUsersCount++;
        }

        return {
            success: true,
            message: "invitation.bulk_invite_success",
            invitedUsers: invitedUsersCount,
            existingUsers: existingUsersCount,
        };

    })
})

// Cancel an invitation
export const cancel = mutation({
    args: InvitationsCancelRequestDTOObjectValidator,
    returns: InvitationsCancelResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsCancelRequestDTO): Promise<InvitationsCancelResponseDTO> => {
        await ctx.db.delete(args.id);
        return {
            success: true,
            message: "invitation.cancel_success",
        };
    })
})

// Bulk cancel invitations
export const bulkCancel = mutation({
    args: InvitationsBulkCancelRequestDTOObjectValidator,
    returns: InvitationsBulkCancelResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsBulkCancelRequestDTO): Promise<InvitationsBulkCancelResponseDTO> => {
        await Promise.all(args.ids.map(async (id) => {
            await ctx.db.delete(id);
        }));
        return {
            success: true,
            message: "invitation.bulk_cancel_success",
        };
    })
})

// Resend the invitation email
export const resend = mutation({
    args: InvitationsResendRequestDTOObjectValidator,
    returns: InvitationsResendResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsResendRequestDTO): Promise<InvitationsResendResponseDTO> => {
        const currentUser = await getCurrentUser(ctx);
        if (!currentUser) {
            return {
                success: false,
                message: "user.not_found",
            };
        }

        // Get the invitation
        const invitation = await ctx.db.get(args.id);
        if (!invitation) {
            return {
                success: false,
                message: "invitation.not_found",
            };
        }

        // Check if invitation is still valid (not accepted)
        if (invitation.accepted) {
            return {
                success: false,
                message: "invitation.already_accepted",
            };
        }

        // Get the role from the invitation
        const role = invitation.roleSuggested?.[0] || Roles.CUSTOMER;

        // Schedule the email to be sent asynchronously
        await ctx.scheduler.runAfter(0, internal.functions.sendInvitationEmail.sendInvitationEmail, {
            email: invitation.email,
            token: invitation.token,
            inviterName: currentUser.name || currentUser.email,
            role,
        });

        return {
            success: true,
            message: "invitation.resend_success",
        };
    })
})

// Bulk resend invitations
export const bulkResend = mutation({
    args: InvitationsBulkResendRequestDTOObjectValidator,
    returns: InvitationsBulkResendResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: MutationCtx, args: InvitationsBulkResendRequestDTO): Promise<InvitationsBulkResendResponseDTO> => {
        const currentUser = await getCurrentUser(ctx);
        if (!currentUser) {
            return {
                success: false,
                message: "user.not_found",
            };
        }

        let resendCount = 0;

        for (const id of args.ids) {
            // Get the invitation
            const invitation = await ctx.db.get(id);
            if (!invitation) {
                continue;
            }

            // Check if invitation is still valid (not accepted)

            if (invitation.accepted) {
                continue;
            }

            // Get the role from the invitation
            const role = invitation.roleSuggested?.[0] || Roles.CUSTOMER;

            // Schedule the email to be sent asynchronously
            await ctx.scheduler.runAfter(0, internal.functions.sendInvitationEmail.sendInvitationEmail, {
                email: invitation.email,
                token: invitation.token,
                inviterName: currentUser.name || currentUser.email,
                role,
            })
            resendCount++;
        }
        return {
            success: true,
            message: "invitation.bulk_resend_success",
            count: resendCount,
        };
    })
})